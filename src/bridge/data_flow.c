#include "bridge/data_flow.h"
#include "common/hex_utils.h"
#include "common/protocol.h"
#include "log.h"
#include "sitp_lib.h"
#include <event2/buffer.h>
#include <event2/bufferevent.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// Forward declarations
static int handle_tcp_to_sitp_message(bridge_data_flow_t *flow, message_t *msg);
static int handle_sitp_to_tcp_message(bridge_data_flow_t *flow, message_t *msg);
static int process_tcp_data_command(bridge_data_flow_t *flow, message_t *msg);
static int process_tcp_disconnect_command(bridge_data_flow_t *flow,
                                          message_t *msg);
static int process_sitp_data_command(bridge_data_flow_t *flow, message_t *msg);

static int process_sitp_disconnect_command(bridge_data_flow_t *flow,
                                           message_t *msg);
static int send_to_sitp(bridge_data_flow_t *flow, uint32_t client_fd,
                        message_cmd_t cmd, const uint8_t *data,
                        uint32_t data_len);
static int send_to_tcp_client(bridge_data_flow_t *flow, uint32_t client_fd,
                              const uint8_t *data, uint32_t data_len);

int bridge_data_flow_init(bridge_data_flow_t *flow, tcp_server_t *tcp_server,
                          sitp_client_t *sitp_client, bridge_pipes_t *pipes,
                          bridge_config_t *config) {
  if (!flow || !tcp_server || !sitp_client || !pipes || !config) {
    log_error("Invalid parameters for data flow initialization");
    return -1;
  }

  flow->tcp_server = tcp_server;
  flow->sitp_client = sitp_client;
  flow->pipes = pipes;
  flow->config = config;

  if (bridge_pipes_set_callbacks(pipes, tcp_to_sitp_pipe_cb,
                                 sitp_to_tcp_pipe_cb, flow, flow) < 0) {
    log_error("Failed to set pipe callbacks");
    return -1;
  }

  log_info("Bridge data flow initialized successfully");
  return 0;
}

void tcp_to_sitp_pipe_cb(int fd, short events, void *arg) {
  bridge_data_flow_t *flow = (bridge_data_flow_t *)arg;
  uint8_t *buffer = NULL;
  size_t len = 0;
  message_t *msg = NULL;

  if (!flow || !(events & EV_READ)) {
    return;
  }

  // Process all available messages in the queue
  while (queue_dequeue(flow->pipes->tcp_to_sitp_queue, &buffer, &len) == 0) {
    // Validate simple message structure
    if (len < sizeof(message_t)) {
      log_warn("Invalid message size received from TCP queue: %zu bytes", len);
      free(buffer);
      continue;
    }

    // Cast buffer directly to message_t and check magic
    message_t *buffer_msg = (message_t *)buffer;
    if (memcmp(buffer_msg->magic, SITP_MAGIC, SITP_MAGIC_LEN) != 0) {
      log_warn("Non-SITP message received from TCP queue (magic check failed)");
      free(buffer);
      continue;
    }
    size_t expected_size = sizeof(message_t) + buffer_msg->data_len;

    if (len < expected_size) {
      log_warn("Incomplete message received from TCP queue: expected=%zu, actual=%zu",
               expected_size, len);
      free(buffer);
      continue;
    }

    // Create a copy of the message for processing
    msg = malloc(expected_size);
    if (!msg) {
      log_error("Failed to allocate memory for message copy");
      free(buffer);
      continue;
    }

    memcpy(msg, buffer, expected_size);

    if (handle_tcp_to_sitp_message(flow, msg) < 0) {
      log_error("Failed to handle TCP to SITP message");
    }

    free(msg);
    free(buffer);
  }
}

void sitp_to_tcp_pipe_cb(int fd, short events, void *arg) {
  bridge_data_flow_t *flow = (bridge_data_flow_t *)arg;
  uint8_t *buffer = NULL;
  size_t len = 0;
  message_t *msg = NULL;

  if (!flow || !(events & EV_READ)) {
    return;
  }

  // Process all available messages in the queue
  while (queue_dequeue(flow->pipes->sitp_to_tcp_queue, &buffer, &len) == 0) {
    if (flow->config->verbose) {
      log_info("Received padded message from SITP (size=%zu):", len);
      print_hexdump(buffer, len);
    }

    // Fast magic check first
    if (!is_sitp_message(buffer, len, flow->config->padding_size)) {
      log_warn("Non-SITP message received from SITP queue (magic check failed)");
      free(buffer);
      continue;
    }

    if (!validate_padded_message(buffer, len, flow->config->padding_size)) {
      log_warn("Invalid padded message received from SITP queue");
      free(buffer);
      continue;
    }

    msg = extract_message_from_padded(buffer, len, flow->config->padding_size);
    if (!msg) {
      log_error("Failed to extract message from padded buffer");
      free(buffer);
      continue;
    }

    if (handle_sitp_to_tcp_message(flow, msg) < 0) {
      log_error("Failed to handle SITP to TCP message");
    }

    free(msg);
    free(buffer);
  }
}

// Handle TCP to SITP message routing
static int handle_tcp_to_sitp_message(bridge_data_flow_t *flow,
                                      message_t *msg) {
  if (!flow || !msg) {
    log_error("Invalid parameters for TCP to SITP message handling");
    return -1;
  }

  switch (msg->cmd) {
  case CMD_DATA:
    return process_tcp_data_command(flow, msg);
  case CMD_DISCONNECT:
    return process_tcp_disconnect_command(flow, msg);
  default:
    log_warn("Unknown command received from TCP: %d", msg->cmd);
    return -1;
  }
}

// Handle SITP to TCP message routing
static int handle_sitp_to_tcp_message(bridge_data_flow_t *flow,
                                      message_t *msg) {
  if (!flow || !msg) {
    log_error("Invalid parameters for SITP to TCP message handling");
    return -1;
  }

  tcp_client_t *client =
      tcp_server_find_client(flow->tcp_server, msg->client_fd);
  if (!client) {
    log_warn("TCP client fd=%u not found for SITP message", msg->client_fd);
    return -1;
  }

  switch (msg->cmd) {
  case CMD_DATA:
    return process_sitp_data_command(flow, msg);
  case CMD_DISCONNECT:
    return process_sitp_disconnect_command(flow, msg);
  default:
    log_warn("Unknown command received from SITP: %d", msg->cmd);
    return -1;
  }
}

// Process TCP data command
static int process_tcp_data_command(bridge_data_flow_t *flow, message_t *msg) {
  log_debug("Forwarding %u bytes from TCP client fd=%u to SITP", msg->data_len,
            msg->client_fd);

  if (flow->config->verbose && msg->data_len > 0) {
    log_info("TCP->SITP Data (client_fd=%u):", msg->client_fd);
    print_hexdump(msg->data, msg->data_len);
  }

  return send_to_sitp(flow, msg->client_fd, CMD_DATA, msg->data, msg->data_len);
}

// Process TCP disconnect command
static int process_tcp_disconnect_command(bridge_data_flow_t *flow,
                                          message_t *msg) {
  log_info("TCP client fd=%u disconnected", msg->client_fd);

  return send_to_sitp(flow, msg->client_fd, CMD_DISCONNECT, NULL, 0);
}

// Process SITP data command
static int process_sitp_data_command(bridge_data_flow_t *flow, message_t *msg) {
  log_debug("Forwarding %u bytes from SITP to TCP client fd=%u", msg->data_len,
            msg->client_fd);

  if (flow->config->verbose && msg->data_len > 0) {
    log_info("SITP->TCP Data (client_fd=%u):", msg->client_fd);
    print_hexdump(msg->data, msg->data_len);
  }

  return send_to_tcp_client(flow, msg->client_fd, msg->data, msg->data_len);
}



// Process SITP disconnect command
static int process_sitp_disconnect_command(bridge_data_flow_t *flow,
                                           message_t *msg) {
  log_info("Received disconnect notification from SITP for client fd=%u",
           msg->client_fd);

  // Find and close the TCP client connection
  tcp_client_t *client =
      tcp_server_find_client(flow->tcp_server, msg->client_fd);
  if (client) {
    log_info("Closing TCP client fd=%u due to SITP disconnect notification",
             msg->client_fd);
    tcp_server_close_client(flow->tcp_server, client);
  } else {
    log_warn("TCP client fd=%u not found for disconnect notification",
             msg->client_fd);
  }

  return 0;
}

// Send data to SITP
static int send_to_sitp(bridge_data_flow_t *flow, uint32_t client_fd,
                        message_cmd_t cmd, const uint8_t *data,
                        uint32_t data_len) {
  if (!flow->sitp_client->sitp_handle) {
    log_warn("SITP handle not available");
    return -1;
  }

  padded_message_t *pmsg = create_padded_message(client_fd, cmd, data, data_len,
                                                 flow->config->padding_size);
  if (!pmsg) {
    log_error("Failed to create padded message for SITP");
    return -1;
  }

  if (flow->config->verbose && cmd == CMD_DATA) {
    log_info("Sending padded message to SITP (total_size=%zu):",
             pmsg->total_size);
    print_hexdump(pmsg->buffer, pmsg->total_size);
  }

  int result = sitp_lib_send(flow->sitp_client->sitp_handle, pmsg->buffer,
                             pmsg->total_size);

  // Apply configurable delay after sending (nanosecond precision, only if > 0)
  if (flow->config->sitp_send_delay_ns > 0) {
    struct timespec delay_spec = {
        .tv_sec = flow->config->sitp_send_delay_ns / 1000000000L,
        .tv_nsec = flow->config->sitp_send_delay_ns % 1000000000L};
    nanosleep(&delay_spec, NULL);
  }

  if (result == 0) {
    log_debug("Successfully sent %zu bytes to SITP (cmd=%d, client_fd=%u)",
              pmsg->total_size, cmd, client_fd);
  } else {
    log_warn("Failed to send data to SITP (cmd=%d, client_fd=%u)", cmd,
             client_fd);
  }

  free_padded_message(pmsg);
  return (result == 0) ? 0 : -1;
}

// Send data to TCP client
static int send_to_tcp_client(bridge_data_flow_t *flow, uint32_t client_fd,
                              const uint8_t *data, uint32_t data_len) {
  tcp_client_t *client = tcp_server_find_client(flow->tcp_server, client_fd);
  if (!client || !client->bev) {
    log_warn("TCP client fd=%u not found or invalid", client_fd);
    return -1;
  }

  if (data_len == 0) {
    log_debug("No data to send to TCP client fd=%u", client_fd);
    return 0;
  }

  struct evbuffer *output = bufferevent_get_output(client->bev);
  if (evbuffer_add(output, data, data_len) == 0) {
    log_debug("Sent %u bytes to TCP client fd=%u", data_len, client_fd);
    return 0;
  } else {
    log_warn("Failed to send data to TCP client fd=%u", client_fd);
    return -1;
  }
}
